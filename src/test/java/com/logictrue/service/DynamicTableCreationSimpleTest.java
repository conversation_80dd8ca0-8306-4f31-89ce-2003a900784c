package com.logictrue.service;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 动态表创建简单测试
 * 使用简单的测试实体类验证动态表创建功能
 */
public class DynamicTableCreationSimpleTest {
    private static final Logger logger = LoggerFactory.getLogger(DynamicTableCreationSimpleTest.class);

    @Test
    @DisplayName("测试实体注解解析功能")
    void testEntityAnnotationParsing() {
        try {
            // 使用测试实体类
            EntityAnnotationParser.TableInfo tableInfo = 
                EntityAnnotationParser.parseEntity(TestEntity.class);
            
            assertNotNull(tableInfo, "表信息不应为null");
            assertEquals("test_entity", tableInfo.getTableName(), "表名应该正确");
            assertFalse(tableInfo.getColumns().isEmpty(), "应该有列信息");
            
            // 验证列信息
            boolean hasIdColumn = false;
            boolean hasNameColumn = false;
            boolean hasStatusColumn = false;
            
            for (EntityAnnotationParser.ColumnInfo column : tableInfo.getColumns()) {
                switch (column.getColumnName()) {
                    case "id":
                        hasIdColumn = true;
                        assertTrue(column.isPrimaryKey(), "id应该是主键");
                        assertTrue(column.isAutoIncrement(), "id应该自增");
                        assertEquals("INTEGER", column.getDataType(), "id应该是INTEGER类型");
                        break;
                    case "name":
                        hasNameColumn = true;
                        assertEquals("TEXT", column.getDataType(), "name应该是TEXT类型");
                        break;
                    case "status":
                        hasStatusColumn = true;
                        assertEquals("INTEGER", column.getDataType(), "status应该是INTEGER类型");
                        break;
                }
            }
            
            assertTrue(hasIdColumn, "应该有id列");
            assertTrue(hasNameColumn, "应该有name列");
            assertTrue(hasStatusColumn, "应该有status列");
            
            logger.info("实体注解解析测试通过");
            
        } catch (Exception e) {
            logger.error("实体注解解析测试失败", e);
            fail("实体注解解析测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试SQL生成功能")
    void testSqlGeneration() {
        try {
            // 解析测试实体类
            EntityAnnotationParser.TableInfo tableInfo = 
                EntityAnnotationParser.parseEntity(TestEntity.class);
            
            // 生成CREATE TABLE语句
            String createTableSql = DynamicSqlGenerator.generateCreateTableSql(tableInfo);
            
            assertNotNull(createTableSql, "CREATE TABLE语句不应为null");
            assertTrue(createTableSql.contains("CREATE TABLE IF NOT EXISTS test_entity"), 
                "应包含正确的表名");
            assertTrue(createTableSql.contains("id INTEGER PRIMARY KEY AUTOINCREMENT"), 
                "应包含主键定义");
            assertTrue(createTableSql.contains("name TEXT"), 
                "应包含name字段定义");
            assertTrue(createTableSql.contains("status INTEGER"), 
                "应包含status字段定义");
            
            logger.info("生成的SQL: {}", createTableSql);
            
            // 生成索引语句
            var indexSqls = DynamicSqlGenerator.generateIndexSql(tableInfo);
            assertNotNull(indexSqls, "索引语句列表不应为null");
            
            logger.info("生成了 {} 个索引", indexSqls.size());
            for (String indexSql : indexSqls) {
                logger.info("索引SQL: {}", indexSql);
            }
            
            logger.info("SQL生成测试通过");
            
        } catch (Exception e) {
            logger.error("SQL生成测试失败", e);
            fail("SQL生成测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试驼峰命名转换")
    void testCamelToUnderscore() {
        // 测试类名转换
        assertEquals("test_entity", 
            EntityAnnotationParser.camelToUnderscore("TestEntity"));
        assertEquals("device_detection_data", 
            EntityAnnotationParser.camelToUnderscore("DeviceDetectionData"));
        
        // 测试字段名转换
        assertEquals("device_code", 
            EntityAnnotationParser.camelToUnderscore("deviceCode"));
        assertEquals("create_time", 
            EntityAnnotationParser.camelToUnderscore("createTime"));
        
        // 测试边界情况
        assertEquals("", EntityAnnotationParser.camelToUnderscore(""));
        assertNull(EntityAnnotationParser.camelToUnderscore(null));
        assertEquals("a", EntityAnnotationParser.camelToUnderscore("a"));
        assertEquals("a_b", EntityAnnotationParser.camelToUnderscore("AB"));
        
        logger.info("驼峰命名转换测试通过");
    }

    @Test
    @DisplayName("测试数据类型映射")
    void testDataTypeMapping() {
        try {
            EntityAnnotationParser.TableInfo tableInfo = 
                EntityAnnotationParser.parseEntity(ComplexTestEntity.class);
            
            for (EntityAnnotationParser.ColumnInfo column : tableInfo.getColumns()) {
                switch (column.getFieldName()) {
                    case "id":
                        assertEquals("INTEGER", column.getDataType());
                        break;
                    case "name":
                        assertEquals("TEXT", column.getDataType());
                        break;
                    case "price":
                        assertEquals("REAL", column.getDataType());
                        break;
                    case "active":
                        assertEquals("INTEGER", column.getDataType());
                        break;
                    case "createTime":
                        assertEquals("TIMESTAMP", column.getDataType());
                        break;
                }
            }
            
            logger.info("数据类型映射测试通过");
            
        } catch (Exception e) {
            logger.error("数据类型映射测试失败", e);
            fail("数据类型映射测试失败: " + e.getMessage());
        }
    }

    // 测试实体类
    @TableName("test_entity")
    public static class TestEntity {
        @TableId(value = "id", type = IdType.AUTO)
        private Long id;
        
        @TableField("name")
        private String name;
        
        @TableField("status")
        private Integer status;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    // 复杂测试实体类
    @TableName("complex_test_entity")
    public static class ComplexTestEntity {
        @TableId(value = "id", type = IdType.AUTO)
        private Long id;
        
        @TableField("name")
        private String name;
        
        @TableField("price")
        private Double price;
        
        @TableField("active")
        private Boolean active;
        
        @TableField("create_time")
        private LocalDateTime createTime;
        
        @TableField(exist = false)
        private String tempField; // 这个字段不会创建对应的数据库列
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Double getPrice() { return price; }
        public void setPrice(Double price) { this.price = price; }
        
        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }
        
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        
        public String getTempField() { return tempField; }
        public void setTempField(String tempField) { this.tempField = tempField; }
    }
}
