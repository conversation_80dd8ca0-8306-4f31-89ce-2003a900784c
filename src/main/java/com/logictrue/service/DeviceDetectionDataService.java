package com.logictrue.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.mapper.DeviceDetectionBasicFieldMapper;
import com.logictrue.mapper.DeviceDetectionDataMapper;
import com.logictrue.mapper.DeviceDetectionTableDataMapper;
import com.logictrue.mapper.DeviceDetectionTableHeaderMapper;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测数据服务类
 * 使用MyBatis-Plus提供DeviceDetectionData在SQLite数据库中的新增和修改操作
 */
public class DeviceDetectionDataService {
    private static final Logger logger = LoggerFactory.getLogger(DeviceDetectionDataService.class);

    private final MyBatisPlusConfig myBatisPlusConfig;
    private final DeviceDetectionDataMapper dataMapper;
    private final DeviceDetectionBasicFieldMapper basicFieldMapper;
    private final DeviceDetectionTableHeaderMapper tableHeaderMapper;
    private final DeviceDetectionTableDataMapper tableDataMapper;

    public DeviceDetectionDataService() {
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();

        // 获取Mapper实例
        SqlSession sqlSession = myBatisPlusConfig.getSqlSession();
        this.dataMapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
        this.basicFieldMapper = sqlSession.getMapper(DeviceDetectionBasicFieldMapper.class);
        this.tableHeaderMapper = sqlSession.getMapper(DeviceDetectionTableHeaderMapper.class);
        this.tableDataMapper = sqlSession.getMapper(DeviceDetectionTableDataMapper.class);
    }

    /**
     * 在解析前插入记录并获取ID
     *
     * @return 插入记录的ID，失败返回null
     */
    public Long insertBeforeParsing(DeviceDetectionData detectionData) {
        try {
            // 设置默认值
            detectionData.setParseStatus(0); // 0-待解析
            detectionData.setCreateTime(LocalDateTime.now());

            // 使用MyBatis-Plus插入
            int result = dataMapper.insert(detectionData);

            if (result > 0) {
                Long id = detectionData.getId();
                logger.info("成功插入设备检测数据记录，ID: {}, 文件: {}", id, detectionData.getFilePath());
                return id;
            }

        } catch (Exception e) {
            logger.error("插入设备检测数据记录失败: {}", detectionData.getFilePath(), e);
        }

        return null;
    }

    /**
     * 解析完成后更新状态
     *
     * @param id 记录ID
     * @param parseStatus 解析状态 (0-待解析，1-解析成功，2-解析失败)
     * @param parseMessage 解析消息
     * @param totalSheets 总工作表数
     * @param parsedSheets 已解析工作表数
     * @param basicFieldsCount 基础字段数量
     * @param tableRowsCount 表格行数
     * @param updateBy 更新人
     * @return 更新是否成功
     */
    public boolean updateAfterParsing(Long id, Integer parseStatus, String parseMessage,
                                     Integer totalSheets, Integer parsedSheets,
                                     Integer basicFieldsCount, Integer tableRowsCount,
                                     String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 使用MyBatis-Plus的自定义更新方法
            int result = dataMapper.updateAfterParsing(id, parseStatus, parseMessage, now,
                    totalSheets, parsedSheets, basicFieldsCount, tableRowsCount, updateBy, now);

            if (result > 0) {
                logger.info("成功更新设备检测数据记录状态，ID: {}, 状态: {}", id, parseStatus);
                return true;
            } else {
                logger.warn("未找到要更新的记录，ID: {}", id);
                return false;
            }

        } catch (Exception e) {
            logger.error("更新设备检测数据记录状态失败，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 根据ID获取记录
     *
     * @param id 记录ID
     * @return 记录信息，未找到返回null
     */
    public DeviceDetectionData getById(Long id) {
        try {
            return dataMapper.selectById(id);
        } catch (Exception e) {
            logger.error("根据ID获取设备检测数据记录失败，ID: {}", id, e);
            return null;
        }
    }

    /**
     * 更新解析状态为进行中
     *
     * @param id 记录ID
     * @return 更新是否成功
     */
    public boolean updateParsingInProgress(Long id) {
        return updateParseStatus(id, 0, "解析进行中");
    }

    /**
     * 更新解析状态为成功
     *
     * @param id 记录ID
     * @param message 成功消息
     * @return 更新是否成功
     */
    public boolean updateParsingSuccess(Long id, String message) {
        return updateParseStatus(id, 1, message != null ? message : "解析成功");
    }

    /**
     * 更新解析状态为失败
     *
     * @param id 记录ID
     * @param errorMessage 错误消息
     * @return 更新是否成功
     */
    public boolean updateParsingFailed(Long id, String errorMessage) {
        return updateParseStatus(id, 2, errorMessage != null ? errorMessage : "解析失败");
    }

    /**
     * 更新解析状态
     *
     * @param id 记录ID
     * @param status 状态
     * @param message 消息
     * @return 更新是否成功
     */
    private boolean updateParseStatus(Long id, Integer status, String message) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 使用MyBatis-Plus的自定义更新方法
            int result = dataMapper.updateParseStatus(id, status, message, now, now);

            if (result > 0) {
                logger.info("成功更新解析状态，ID: {}, 状态: {}, 消息: {}", id, status, message);
                return true;
            } else {
                logger.warn("未找到要更新的记录，ID: {}", id);
                return false;
            }

        } catch (Exception e) {
            logger.error("更新解析状态失败，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 批量插入基础字段数据
     *
     * @param detectionDataId 检测数据ID
     * @param basicFields 基础字段列表
     * @return 插入是否成功
     */
    public boolean batchInsertBasicFields(Long detectionDataId, List<DeviceDetectionBasicField> basicFields) {
        if (basicFields == null || basicFields.isEmpty()) {
            logger.warn("基础字段列表为空，跳过插入");
            return true;
        }

        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionBasicField field : basicFields) {
                field.setDetectionDataId(detectionDataId);
                field.setCreateTime(now);
            }

            // 使用MyBatis-Plus批量插入
            int insertCount = 0;
            for (DeviceDetectionBasicField field : basicFields) {
                insertCount += basicFieldMapper.insert(field);
            }

            if (insertCount == basicFields.size()) {
                logger.info("成功批量插入基础字段数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入基础字段数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, basicFields.size(), insertCount);
                return false;
            }

        } catch (Exception e) {
            logger.error("批量插入基础字段数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 批量插入表头数据
     *
     * @param detectionDataId 检测数据ID
     * @param tableHeaders 表头列表
     * @return 插入是否成功
     */
    public boolean batchInsertTableHeaders(Long detectionDataId, List<DeviceDetectionTableHeader> tableHeaders) {
        if (tableHeaders == null || tableHeaders.isEmpty()) {
            logger.warn("表头列表为空，跳过插入");
            return true;
        }

        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionTableHeader header : tableHeaders) {
                header.setDetectionDataId(detectionDataId);
                header.setCreateTime(now);
            }

            // 使用MyBatis-Plus批量插入
            int insertCount = 0;
            for (DeviceDetectionTableHeader header : tableHeaders) {
                insertCount += tableHeaderMapper.insert(header);
            }

            if (insertCount == tableHeaders.size()) {
                logger.info("成功批量插入表头数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入表头数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, tableHeaders.size(), insertCount);
                return false;
            }

        } catch (Exception e) {
            logger.error("批量插入表头数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 批量插入表格数据
     *
     * @param detectionDataId 检测数据ID
     * @param tableData 表格数据列表
     * @return 插入是否成功
     */
    public boolean batchInsertTableData(Long detectionDataId, List<DeviceDetectionTableData> tableData) {
        if (tableData == null || tableData.isEmpty()) {
            logger.warn("表格数据列表为空，跳过插入");
            return true;
        }

        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionTableData data : tableData) {
                data.setDetectionDataId(detectionDataId);
                data.setCreateTime(now);
            }

            // 使用MyBatis-Plus批量插入
            int insertCount = 0;
            for (DeviceDetectionTableData data : tableData) {
                insertCount += tableDataMapper.insert(data);
            }

            if (insertCount == tableData.size()) {
                logger.info("成功批量插入表格数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入表格数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, tableData.size(), insertCount);
                return false;
            }

        } catch (Exception e) {
            logger.error("批量插入表格数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }

    /**
     * 添加查询方法
     */

    /**
     * 分页查询设备检测数据
     */
    public List<DeviceDetectionData> getPageData(int page, int pageSize) {
        try {
            int offset = (page - 1) * pageSize;
            return dataMapper.selectPageData(offset, pageSize);
        } catch (Exception e) {
            logger.error("分页查询设备检测数据失败，页码: {}, 页大小: {}", page, pageSize, e);
            return null;
        }
    }

    /**
     * 根据设备编码查询
     */
    public List<DeviceDetectionData> getByDeviceCode(String deviceCode) {
        try {
            return dataMapper.selectByDeviceCode(deviceCode);
        } catch (Exception e) {
            logger.error("根据设备编码查询失败，设备编码: {}", deviceCode, e);
            return null;
        }
    }

    /**
     * 根据解析状态查询
     */
    public List<DeviceDetectionData> getByParseStatus(Integer parseStatus) {
        try {
            return dataMapper.selectByParseStatus(parseStatus);
        } catch (Exception e) {
            logger.error("根据解析状态查询失败，状态: {}", parseStatus, e);
            return null;
        }
    }

    /**
     * 统计总记录数
     */
    public long getTotalCount() {
        try {
            return dataMapper.countTotal();
        } catch (Exception e) {
            logger.error("统计总记录数失败", e);
            return 0;
        }
    }

}
