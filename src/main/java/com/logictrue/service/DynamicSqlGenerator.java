package com.logictrue.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态SQL生成器
 * 根据实体类注解信息生成SQLite的CREATE TABLE语句
 */
public class DynamicSqlGenerator {
    private static final Logger logger = LoggerFactory.getLogger(DynamicSqlGenerator.class);

    /**
     * 生成CREATE TABLE语句
     */
    public static String generateCreateTableSql(EntityAnnotationParser.TableInfo tableInfo) {
        logger.info("开始生成CREATE TABLE语句，表名: {}", tableInfo.getTableName());

        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS ").append(tableInfo.getTableName()).append(" (");

        // 生成列定义
        List<String> columnDefinitions = new ArrayList<>();
        List<String> foreignKeys = new ArrayList<>();

        for (EntityAnnotationParser.ColumnInfo column : tableInfo.getColumns()) {
            String columnDef = generateColumnDefinition(column);
            columnDefinitions.add(columnDef);

            // 检查是否需要外键约束
            String foreignKey = generateForeignKeyConstraint(column, tableInfo.getTableName());
            if (foreignKey != null) {
                foreignKeys.add(foreignKey);
            }
        }

        // 添加列定义
        sql.append(String.join(", ", columnDefinitions));

        // 添加外键约束
        if (!foreignKeys.isEmpty()) {
            sql.append(", ").append(String.join(", ", foreignKeys));
        }

        sql.append(")");

        String result = sql.toString();
        logger.info("CREATE TABLE语句生成完成: {}", result);
        return result;
    }

    /**
     * 生成列定义
     */
    private static String generateColumnDefinition(EntityAnnotationParser.ColumnInfo column) {
        StringBuilder columnDef = new StringBuilder();

        // 列名
        columnDef.append(column.getColumnName());

        // 数据类型
        columnDef.append(" ").append(column.getDataType());

        // 主键
        if (column.isPrimaryKey()) {
            columnDef.append(" PRIMARY KEY");

            // 自增
            if (column.isAutoIncrement()) {
                columnDef.append(" AUTOINCREMENT");
            }
        }

        // 非空约束
        if (column.isNotNull() && !column.isPrimaryKey()) {
            columnDef.append(" NOT NULL");
        }

        // 默认值
        if (column.getDefaultValue() != null && !column.getDefaultValue().isEmpty()) {
            if ("CURRENT_TIMESTAMP".equals(column.getDefaultValue())) {
                columnDef.append(" DEFAULT CURRENT_TIMESTAMP");
            } else if (isNumeric(column.getDefaultValue())) {
                columnDef.append(" DEFAULT ").append(column.getDefaultValue());
            } else {
                columnDef.append(" DEFAULT '").append(column.getDefaultValue()).append("'");
            }
        }

        return columnDef.toString();
    }

    /**
     * 生成外键约束
     */
    private static String generateForeignKeyConstraint(EntityAnnotationParser.ColumnInfo column, String currentTableName) {
        String columnName = column.getColumnName();

        // 根据命名约定推断外键关系
        if (columnName.endsWith("_id") && !columnName.equals("id")) {
            String referencedTable = inferReferencedTable(columnName, currentTableName);
            if (referencedTable != null) {
                return String.format("FOREIGN KEY (%s) REFERENCES %s(id)", columnName, referencedTable);
            }
        }

        return null;
    }

    /**
     * 推断引用的表名
     */
    private static String inferReferencedTable(String columnName, String currentTableName) {
        // 移除_id后缀
        String baseName = columnName.substring(0, columnName.length() - 3);

        // 根据业务逻辑推断表名
        switch (baseName) {
            case "detection_data":
                return "device_detection_data";
            case "template":
                return "excel_template";
            case "device":
                return "device_info";
            default:
                // 默认规则：基础名称 + 表名模式
                if (currentTableName.startsWith("device_detection_")) {
                    return "device_detection_data";
                }
                return null;
        }
    }

    /**
     * 生成索引语句
     */
    public static List<String> generateIndexSql(EntityAnnotationParser.TableInfo tableInfo) {
        List<String> indexSqls = new ArrayList<>();
        String tableName = tableInfo.getTableName();

        // 为常用查询字段创建索引
        for (EntityAnnotationParser.ColumnInfo column : tableInfo.getColumns()) {
            if (shouldCreateIndex(column)) {
                String indexName = String.format("idx_%s_%s", tableName, column.getColumnName());
                String indexSql = String.format("CREATE INDEX IF NOT EXISTS %s ON %s (%s)",
                    indexName, tableName, column.getColumnName());
                indexSqls.add(indexSql);
            }
        }

        // 为外键字段创建索引
        for (EntityAnnotationParser.ColumnInfo column : tableInfo.getColumns()) {
            if (column.getColumnName().endsWith("_id") && !column.getColumnName().equals("id")) {
                String indexName = String.format("idx_%s_%s", tableName, column.getColumnName());
                String indexSql = String.format("CREATE INDEX IF NOT EXISTS %s ON %s (%s)",
                    indexName, tableName, column.getColumnName());
                if (!indexSqls.contains(indexSql)) {
                    indexSqls.add(indexSql);
                }
            }
        }

        return indexSqls;
    }

    /**
     * 判断是否应该为字段创建索引
     */
    private static boolean shouldCreateIndex(EntityAnnotationParser.ColumnInfo column) {
        String columnName = column.getColumnName().toLowerCase();

        // 主键不需要额外索引
        if (column.isPrimaryKey()) {
            return false;
        }

        // 常用查询字段
        return columnName.contains("code") ||
               columnName.contains("name") ||
               columnName.contains("status") ||
               columnName.contains("type") ||
               columnName.endsWith("_time");
    }

    /**
     * 生成完整的表创建脚本
     */
    public static List<String> generateCompleteTableScript(EntityAnnotationParser.TableInfo tableInfo) {
        List<String> scripts = new ArrayList<>();

        // 创建表语句
        scripts.add(generateCreateTableSql(tableInfo));

        // 创建索引语句
        scripts.addAll(generateIndexSql(tableInfo));

        return scripts;
    }

    /**
     * 生成表结构信息查询SQL
     */
    public static String generateTableInfoSql(String tableName) {
        return String.format("PRAGMA table_info(%s)", tableName);
    }

    /**
     * 生成检查表是否存在的SQL
     */
    public static String generateTableExistsSql(String tableName) {
        return String.format(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='%s'",
            tableName
        );
    }

    /**
     * 生成ALTER TABLE语句（用于表结构变更）
     */
    public static List<String> generateAlterTableSql(String tableName,
                                                   List<EntityAnnotationParser.ColumnInfo> newColumns) {
        List<String> alterSqls = new ArrayList<>();

        for (EntityAnnotationParser.ColumnInfo column : newColumns) {
            String alterSql = String.format("ALTER TABLE %s ADD COLUMN %s",
                tableName, generateColumnDefinition(column));
            alterSqls.add(alterSql);
        }

        return alterSqls;
    }

    /**
     * 判断字符串是否为数字
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 生成DROP TABLE语句
     */
    public static String generateDropTableSql(String tableName) {
        return String.format("DROP TABLE IF EXISTS %s", tableName);
    }

    /**
     * 生成TRUNCATE TABLE语句（SQLite使用DELETE）
     */
    public static String generateTruncateTableSql(String tableName) {
        return String.format("DELETE FROM %s", tableName);
    }

    /**
     * 格式化SQL语句（美化输出）
     */
    public static String formatSql(String sql) {
        return sql.replaceAll(",", ",\n    ")
                 .replaceAll("\\(", "(\n    ")
                 .replaceAll("\\)", "\n)");
    }
}
