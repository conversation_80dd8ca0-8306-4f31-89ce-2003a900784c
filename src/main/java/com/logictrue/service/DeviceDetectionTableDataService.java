package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.mapper.DeviceDetectionTableDataMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测表格数据Service
 */
public class DeviceDetectionTableDataService extends BaseService<DeviceDetectionTableData, DeviceDetectionTableDataMapper> {
    
    public DeviceDetectionTableDataService() {
        initService();
    }
    
    @Override
    protected DeviceDetectionTableDataMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionTableDataMapper.class);
    }
    
    /**
     * 批量插入表格数据
     * 
     * @param detectionDataId 检测数据ID
     * @param tableData 表格数据列表
     * @return 插入是否成功
     */
    public boolean batchInsertTableData(Long detectionDataId, List<DeviceDetectionTableData> tableData) {
        if (tableData == null || tableData.isEmpty()) {
            logger.warn("表格数据列表为空，跳过插入");
            return true;
        }
        
        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionTableData data : tableData) {
                data.setDetectionDataId(detectionDataId);
                data.setCreateTime(now);
            }
            
            // 使用批量插入
            int insertCount = batchInsert(tableData);
            
            if (insertCount == tableData.size()) {
                logger.info("成功批量插入表格数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入表格数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, tableData.size(), insertCount);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("批量插入表格数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }
    
    /**
     * 根据检测数据ID查询表格数据
     */
    public List<DeviceDetectionTableData> getByDetectionDataId(Long detectionDataId) {
        try {
            return mapper.selectByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表格数据失败，ID: {}", detectionDataId, e);
            return null;
        }
    }
    
    /**
     * 根据检测数据ID和工作表ID查询表格数据
     */
    public List<DeviceDetectionTableData> getByDetectionDataIdAndSheetId(Long detectionDataId, String sheetId) {
        try {
            return mapper.selectByDetectionDataIdAndSheetId(detectionDataId, sheetId);
        } catch (Exception e) {
            logger.error("根据检测数据ID和工作表ID查询表格数据失败，检测数据ID: {}, 工作表ID: {}", detectionDataId, sheetId, e);
            return null;
        }
    }
    
    /**
     * 根据检测数据ID统计表格数据行数
     */
    public long countByDetectionDataId(Long detectionDataId) {
        try {
            return mapper.countByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID统计表格数据行数失败，ID: {}", detectionDataId, e);
            return 0;
        }
    }
    
    /**
     * 根据检测数据ID删除表格数据
     */
    public boolean deleteByDetectionDataId(Long detectionDataId) {
        try {
            int result = mapper.deleteByDetectionDataId(detectionDataId);
            logger.info("删除表格数据，检测数据ID: {}, 删除数量: {}", detectionDataId, result);
            return result > 0;
        } catch (Exception e) {
            logger.error("根据检测数据ID删除表格数据失败，ID: {}", detectionDataId, e);
            return false;
        }
    }
    
    /**
     * 分页查询表格数据
     */
    public List<DeviceDetectionTableData> getPageByDetectionDataId(Long detectionDataId, int offset, int pageSize) {
        try {
            return mapper.selectPageByDetectionDataId(detectionDataId, offset, pageSize);
        } catch (Exception e) {
            logger.error("分页查询表格数据失败，检测数据ID: {}, offset: {}, pageSize: {}", detectionDataId, offset, pageSize, e);
            return null;
        }
    }
}
