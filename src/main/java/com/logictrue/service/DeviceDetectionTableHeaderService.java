package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.mapper.DeviceDetectionTableHeaderMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测表头Service
 */
public class DeviceDetectionTableHeaderService extends BaseService<DeviceDetectionTableHeader, DeviceDetectionTableHeaderMapper> {
    
    public DeviceDetectionTableHeaderService() {
        initService();
    }
    
    @Override
    protected DeviceDetectionTableHeaderMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionTableHeaderMapper.class);
    }
    
    /**
     * 批量插入表头数据
     * 
     * @param detectionDataId 检测数据ID
     * @param tableHeaders 表头列表
     * @return 插入是否成功
     */
    public boolean batchInsertTableHeaders(Long detectionDataId, List<DeviceDetectionTableHeader> tableHeaders) {
        if (tableHeaders == null || tableHeaders.isEmpty()) {
            logger.warn("表头列表为空，跳过插入");
            return true;
        }
        
        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionTableHeader header : tableHeaders) {
                header.setDetectionDataId(detectionDataId);
                header.setCreateTime(now);
            }
            
            // 使用批量插入
            int insertCount = batchInsert(tableHeaders);
            
            if (insertCount == tableHeaders.size()) {
                logger.info("成功批量插入表头数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入表头数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, tableHeaders.size(), insertCount);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("批量插入表头数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }
    
    /**
     * 根据检测数据ID查询表头
     */
    public List<DeviceDetectionTableHeader> getByDetectionDataId(Long detectionDataId) {
        try {
            return mapper.selectByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表头失败，ID: {}", detectionDataId, e);
            return null;
        }
    }
    
    /**
     * 根据检测数据ID和工作表ID查询表头
     */
    public List<DeviceDetectionTableHeader> getByDetectionDataIdAndSheetId(Long detectionDataId, String sheetId) {
        try {
            return mapper.selectByDetectionDataIdAndSheetId(detectionDataId, sheetId);
        } catch (Exception e) {
            logger.error("根据检测数据ID和工作表ID查询表头失败，检测数据ID: {}, 工作表ID: {}", detectionDataId, sheetId, e);
            return null;
        }
    }
}
