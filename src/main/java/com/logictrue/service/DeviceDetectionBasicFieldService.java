package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.mapper.DeviceDetectionBasicFieldMapper;
import org.apache.ibatis.session.SqlSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测基础字段Service
 */
public class DeviceDetectionBasicFieldService extends BaseService<DeviceDetectionBasicField, DeviceDetectionBasicFieldMapper> {
    
    public DeviceDetectionBasicFieldService() {
        initService();
    }
    
    @Override
    protected DeviceDetectionBasicFieldMapper getMapper(SqlSession sqlSession) {
        return sqlSession.getMapper(DeviceDetectionBasicFieldMapper.class);
    }
    
    /**
     * 批量插入基础字段数据
     * 
     * @param detectionDataId 检测数据ID
     * @param basicFields 基础字段列表
     * @return 插入是否成功
     */
    public boolean batchInsertBasicFields(Long detectionDataId, List<DeviceDetectionBasicField> basicFields) {
        if (basicFields == null || basicFields.isEmpty()) {
            logger.warn("基础字段列表为空，跳过插入");
            return true;
        }
        
        try {
            // 设置检测数据ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (DeviceDetectionBasicField field : basicFields) {
                field.setDetectionDataId(detectionDataId);
                field.setCreateTime(now);
            }
            
            // 使用批量插入
            int insertCount = batchInsert(basicFields);
            
            if (insertCount == basicFields.size()) {
                logger.info("成功批量插入基础字段数据，检测数据ID: {}, 插入数量: {}", detectionDataId, insertCount);
                return true;
            } else {
                logger.error("批量插入基础字段数据部分失败，检测数据ID: {}, 期望: {}, 实际: {}",
                        detectionDataId, basicFields.size(), insertCount);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("批量插入基础字段数据失败，检测数据ID: {}", detectionDataId, e);
            return false;
        }
    }
    
    /**
     * 根据检测数据ID查询基础字段
     */
    public List<DeviceDetectionBasicField> getByDetectionDataId(Long detectionDataId) {
        try {
            return mapper.selectByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询基础字段失败，ID: {}", detectionDataId, e);
            return null;
        }
    }
    
    /**
     * 根据检测数据ID和工作表ID查询基础字段
     */
    public List<DeviceDetectionBasicField> getByDetectionDataIdAndSheetId(Long detectionDataId, String sheetId) {
        try {
            return mapper.selectByDetectionDataIdAndSheetId(detectionDataId, sheetId);
        } catch (Exception e) {
            logger.error("根据检测数据ID和工作表ID查询基础字段失败，检测数据ID: {}, 工作表ID: {}", detectionDataId, sheetId, e);
            return null;
        }
    }
    
    /**
     * 根据检测数据ID统计基础字段数量
     */
    public long countByDetectionDataId(Long detectionDataId) {
        try {
            return mapper.countByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID统计基础字段数量失败，ID: {}", detectionDataId, e);
            return 0;
        }
    }
    
    /**
     * 根据检测数据ID删除基础字段
     */
    public boolean deleteByDetectionDataId(Long detectionDataId) {
        try {
            int result = mapper.deleteByDetectionDataId(detectionDataId);
            logger.info("删除基础字段数据，检测数据ID: {}, 删除数量: {}", detectionDataId, result);
            return result > 0;
        } catch (Exception e) {
            logger.error("根据检测数据ID删除基础字段失败，ID: {}", detectionDataId, e);
            return false;
        }
    }
}
