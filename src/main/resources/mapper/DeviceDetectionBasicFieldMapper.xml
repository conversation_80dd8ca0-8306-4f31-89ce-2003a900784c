<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.mapper.DeviceDetectionBasicFieldMapper">

    <!-- 批量插入基础字段数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO device_detection_basic_field (
            detection_data_id,
            sheet_id,
            sheet_name,
            field_code,
            field_name,
            field_value,
            label_position,
            value_position,
            field_type,
            label_row_index,
            label_col_index,
            value_row_index,
            value_col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.fieldCode},
                #{item.fieldName},
                #{item.fieldValue},
                #{item.labelPosition},
                #{item.valuePosition},
                #{item.fieldType},
                #{item.labelRowIndex},
                #{item.labelColIndex},
                #{item.valueRowIndex},
                #{item.valueColIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入基础字段数据（忽略重复） -->
    <insert id="batchInsertIgnore" parameterType="java.util.List">
        INSERT OR IGNORE INTO device_detection_basic_field (
            detection_data_id,
            sheet_id,
            sheet_name,
            field_code,
            field_name,
            field_value,
            label_position,
            value_position,
            field_type,
            label_row_index,
            label_col_index,
            value_row_index,
            value_col_index,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.detectionDataId},
                #{item.sheetId},
                #{item.sheetName},
                #{item.fieldCode},
                #{item.fieldName},
                #{item.fieldValue},
                #{item.labelPosition},
                #{item.valuePosition},
                #{item.fieldType},
                #{item.labelRowIndex},
                #{item.labelColIndex},
                #{item.valueRowIndex},
                #{item.valueColIndex},
                #{item.createTime}
            )
        </foreach>
    </insert>


    <!-- 批量删除基础字段数据 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM device_detection_basic_field
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
